<odoo>

    <!-- Wizard Form View with modern style and icon -->
    <record id="imex_inventory_report_wizard_form" model="ir.ui.view">
        <field name="name">imex.inventory.report.wizard.form</field>
        <field name="model">imex.inventory.report.wizard</field>
        <field name="arch" type="xml">
            <form string="Imex Inventory Report Wizard">
                <sheet>
                    <div class="oe_title" style="margin-bottom: 16px;">
                        <h2 style="display: flex; align-items: center; gap: 8px;">
                            <span class="fa fa-bar-chart" style="color: #1976d2; font-size: 28px;"></span>
                            <span>Inventory Report Wizard</span>
                        </h2>
                        <p style="color: #888; font-size: 13px; margin-top: 4px;">
                            Sélectionnez vos critères pour générer un rapport d'inventaire détaillé et visuel.
                        </p>
                    </div>
                    <group name="criteria" string="Filtres du rapport" style="border: 1px solid #e0e0e0; border-radius: 8px; padding: 16px; background: #fafbfc;">
                        <group>
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="is_groupby_location"/>
                            <field name="len_product" invisible="1"/>
                        </group>
                        <group>
                            <field name="location_id" options="{'no_open': True, 'no_create': True}" domain="[('usage','=','internal'),('active','=',True)]"/>
                            <field name="product_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}" domain="[('active', '=', True), ('detailed_type', '=', 'product')]"/>
                            <field name="product_category_ids" widget="many2many_tags" options="{'no_open': True, 'no_create': True}"/>
                        </group>
                    </group>
                    <footer>
                        <button name="button_view" string="Voir le rapport" type="object" class="oe_highlight" icon="fa-bar-chart"/>
                        <button name="button_view_details" string="Voir les détails" type="object" class="oe_highlight" invisible="len_product != 1" icon="fa-search"/>
                        <button string="Annuler" class="oe_link" special="cancel" />
                    </footer>
                </sheet>
            </form>
        </field>
    </record>

    <record id="imex_inventory_report_action" model="ir.actions.act_window">
        <field name="name">Imex Inventory Report</field>
        <field name="res_model">imex.inventory.report.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <menuitem id="imex_inventory_report_menu" action="imex_inventory_report_action" parent="stock.menu_warehouse_report" sequence="30" />

</odoo>