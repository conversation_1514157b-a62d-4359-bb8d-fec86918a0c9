<odoo>
    <record id="action_imex_inventory_details_report_html" model="ir.actions.report">
        <field name="name">Imex Inventory Details Report</field>
        <field name="model">imex.inventory.details.report</field>
        <field name="report_type">qweb-html</field>
        <field name="report_name">imex_inventory_report.imex_inventory_details_report_html</field>
        <field name="report_file">imex_inventory_report.imex_inventory_details_report_html</field>
    </record>
    <template id="imex_inventory_details_report_html">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <t t-set="docs" t-value="env['imex.inventory.details.report'].browse(detail_ids)"/>
                <div class="o_inventory_reports_page" style="max-width: 1100px; margin: 0 auto; background: #fff; border-radius: 12px; box-shadow: 0 2px 16px #e0e0e0; padding: 32px 32px 24px 32px;">
                    <link href="/imex_inventory_report/static/src/css/report.css" rel="stylesheet" />
                    <div style="text-align:center; margin-bottom: 24px;">
                        <span class="fa fa-bar-chart" style="font-size: 32px; color: #1976d2; vertical-align: middle; margin-right: 8px;"></span>
                        <span style="font-size: 28px; font-weight: 700; color: #222; letter-spacing: 1px;">Imex Inventory Report</span>
                    </div>
                    <t t-call="imex_inventory_report.report_imex_inventory_details_report_base" />
                </div>
            </t>
        </t>
    </template>
    <template id="report_imex_inventory_details_report_base">
        <div class="page">
            <div class="row">
                <h4 class="mt0" style="text-align: center; font-size: 22px; font-weight: 600; color: #1976d2; margin-bottom: 18px;">
                    <span>Imex Inventory report - </span>
                    <t t-out="product_default_code" />
                    <span> - </span>
                    <t t-out="product_name" />
                </h4>
            </div>
            <!-- Display filters -->
            <div style="margin-bottom: 18px;">
                <t t-call="imex_inventory_report.report_imex_inventory_details_report_filters" />
            </div>
            <!-- Display Inventory table -->
            <div class="act_as_table data_table mt4" style="width: 100%; border-radius: 8px; overflow: hidden;">
                <!-- Display header line-->
                <t t-call="imex_inventory_report.report_imex_inventory_details_lines_header" />
                <!-- Display initial lines -->
                <t t-if="docs">
                    <t t-set="line_initial" t-value="docs.filtered(lambda l: not l.product_id)"/>
                    <t t-set="lines" t-value="docs.filtered(lambda l: l.product_id)"/>
                    <t t-if="line_initial">
                        <div class="act_as_row lines" style="background: #e3f2fd; font-weight: bold;">
                            <div class="act_as_cell" />
                            <div class="act_as_cell">Initial</div>
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell" />
                            <div class="act_as_cell right">
                                <t t-out="'{0:,.2f}'.format(line_initial[0].initial)"/>
                            </div>
                            <div class="act_as_cell right">
                                <t t-out="'{0:,.2f}'.format(line_initial[0].initial_amount)"/>
                            </div>
                        </div>
                        <!-- Display each lines -->
                        <t t-set="product_balance" t-value="line_initial[0].initial" />
                        <t t-set="product_amount" t-value="line_initial[0].initial_amount" />
                        <t t-if="lines">
                            <t t-foreach="lines" t-as="product_line">
                                <t t-set="product_balance" t-value="product_balance + product_line.product_in - product_line.product_out" />
                                <t t-set="product_amount" t-value="product_amount + product_line.product_in * product_line.unit_cost - product_line.product_out * product_line.unit_cost" />
                                <t t-call="imex_inventory_report.report_imex_inventory_details_lines" />
                            </t>
                        </t>
                    </t>
                </t>
            </div>
            <p style="page-break-before:always;" />
        </div>
    </template>

    <template id="imex_inventory_report.report_imex_inventory_details_report_filters">
        <div class="act_as_table data_table" style="width: 100%; margin-bottom: 18px; border-radius: 8px; background: #f5f7fa; box-shadow: 0 1px 6px #e0e0e0;">
            <div class="act_as_row labels" style="background: #1976d2; color: #fff; font-weight: 600;">
                <div class="act_as_cell">Date From</div>
                <div class="act_as_cell">Date To</div>
                <div class="act_as_cell">Location</div>
                <div class="act_as_cell">Category</div>
            </div>
            <div class="act_as_row">
                <div class="act_as_cell">
                    <span t-out="date_from" />
                </div>
                <div class="act_as_cell">
                    <span t-out="date_to" />
                </div>
                <div class="act_as_cell">
                    <span t-if="location">
                        <t t-out="location"/>
                    </span>
                    <span t-else="">All stocks</span>
                </div>
                <div class="act_as_cell">
                    <span t-if="category" t-out="category"/>
                </div>
            </div>
        </div>
    </template>

    <template id="imex_inventory_report.report_imex_inventory_details_lines_header">
        <div class="act_as_thead">
            <div class="act_as_row labels" style="background: #1976d2; color: #fff; font-weight: 600;">
                <div class="act_as_cell">Date</div>
                <div class="act_as_cell">Reference</div>
                <div class="act_as_cell">Partner</div>
                <div class="act_as_cell">Source Location</div>
                <div class="act_as_cell">Dest Location</div>
                <div class="act_as_cell">Price</div>
                <div class="act_as_cell">In</div>
                <div class="act_as_cell">Out</div>
                <div class="act_as_cell">Balance</div>
                <div class="act_as_cell">Amount</div>
            </div>
        </div>
    </template>

    <template id="imex_inventory_report.report_imex_inventory_details_lines">
        <div class="act_as_row lines" style="display: table-row;">
            <div class="act_as_cell left" style="display: table-cell;">
                <t t-if="product_line.date"><t t-out="product_line.date.strftime('%Y-%m-%d')" /></t>
            </div>
            <div class="act_as_cell left" style="display: table-cell;">
                <t t-if="product_line.display_name"><t t-out="product_line.display_name" /></t>
            </div>
            <div class="act_as_cell left" style="display: table-cell;">
                <t t-if="product_line.picking_id.partner_id.name"><t t-out="product_line.picking_id.partner_id.name" /></t>
            </div>
            <div class="act_as_cell left" style="display: table-cell;">
                <t t-if="product_line.location_id.complete_name"><t t-out="product_line.location_id.complete_name" /></t>
            </div>
            <div class="act_as_cell left" style="display: table-cell;">
                <t t-if="product_line.location_dest_id.complete_name"><t t-out="product_line.location_dest_id.complete_name" /></t>
            </div>
            <div class="act_as_cell right" style="display: table-cell;">
                <t t-if="product_line.unit_cost is not None"><t t-out="'{0:,.2f}'.format(product_line.unit_cost)" /></t>
            </div>
            <div class="act_as_cell right" style="display: table-cell;">
                <span style="display:inline-block; min-width:40px; border-radius:8px; padding:2px 8px; color:#fff; background:{{ product_line.product_in > 0 and '#43a047' or '#bdbdbd' }}; font-weight:600;">
                    <t t-if="product_line.product_in is not None"><t t-out="'{0:,.2f}'.format(product_line.product_in)" /></t>
                </span>
            </div>
            <div class="act_as_cell right" style="display: table-cell;">
                <span style="display:inline-block; min-width:40px; border-radius:8px; padding:2px 8px; color:#fff; background:{{ product_line.product_out > 0 and '#e53935' or '#bdbdbd' }}; font-weight:600;">
                    <t t-if="product_line.product_out is not None"><t t-out="'{0:,.2f}'.format(product_line.product_out)" /></t>
                </span>
            </div>
            <div class="act_as_cell right" style="display: table-cell;">
                <span style="display:inline-block; min-width:40px; border-radius:8px; padding:2px 8px; color:#fff; background:{{ product_balance >= 0 and '#1976d2' or '#e53935' }}; font-weight:600;">
                    <t t-if="product_balance is not None"><t t-out="'{0:,.2f}'.format(product_balance)" /></t>
                </span>
            </div>
            <div class="act_as_cell right" style="display: table-cell;">
                <t t-if="product_amount is not None"><t t-out="'{0:,.2f}'.format(product_amount)" /></t>
            </div>
        </div>
    </template>
</odoo>