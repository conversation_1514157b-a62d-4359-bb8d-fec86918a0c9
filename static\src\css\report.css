.act_as_table {
    display: table ;
    background-color: white;
}
.act_as_row {
    display: table-row ;
    page-break-inside: avoid;
}
.act_as_cell {
    display: table-cell ;
    page-break-inside: avoid;
}
.act_as_thead {
    display: table-header-group ;
}
.act_as_row.labels {
    background-color: #f0f0f0;
}
.data_table {
    width: 100% ;
    border-left: 0px;
    border-right: 0px;
    text-align: center;
    font-size: 10px;
    padding-right: 3px;
    padding-left: 3px;
    padding-top: 2px;
    padding-bottom: 2px;
    border-collapse: collapse;
}
.data_table .act_as_cell {
    border: 1px solid lightGrey;
    text-align: center;
}
.data_table .act_as_cell {
    word-wrap: break-word;
}
.data_table .act_as_row.labels {
    font-weight: bold;
}
.act_as_cell.left {
    text-align: left;
}
.act_as_cell.right {
    text-align: right;
}
.custom_footer {
    font-size: 7px ;
}
.button_row {
    padding-bottom: 10px;
}
.o_inventory_reports_page {
    padding-top: 10px;
    width: 90%;
    margin-right: auto;
    margin-left: auto;
}

/* Modern badge styles for Odoo tree views */
.o_badge.o_field_widget {
    border-radius: 12px;
    padding: 2px 10px;
    font-weight: bold;
    font-size: 12px;
    color: #fff;
    background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
    box-shadow: 0 1px 3px rgba(25, 118, 210, 0.08);
}
.o_badge.o_field_widget.o_field_widget_decoration-success {
    background: linear-gradient(90deg, #43a047 0%, #66bb6a 100%);
}
.o_badge.o_field_widget.o_field_widget_decoration-danger {
    background: linear-gradient(90deg, #e53935 0%, #ff7043 100%);
}

/* Wizard header style */
.oe_title h2 {
    font-size: 24px;
    font-weight: 700;
    color: #1976d2;
    margin-bottom: 0;
}
.oe_title p {
    font-size: 13px;
    color: #888;
}

/* Section style for wizard */
.o_group.criteria {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 16px;
    background: #fafbfc;
}
